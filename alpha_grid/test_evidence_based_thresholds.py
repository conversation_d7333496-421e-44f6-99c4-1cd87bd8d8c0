#!/usr/bin/env python3
"""
Test Evidence-Based Threshold Implementation
===========================================

Comprehensive test to validate the evidence-based event detection system
with realistic crypto volatility thresholds.
"""

import sys
import os
import time
import asyncio
from datetime import datetime

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_evidence_based_detector():
    """Test the evidence-based event detector with realistic data."""
    try:
        print("🧪 Testing Evidence-Based Event Detector...")
        
        # Import the detector
        from agents.streaming.evidence_based_event_detector import EvidenceBasedEventDetector
        
        # Initialize detector
        detector = EvidenceBasedEventDetector()
        print("✅ Evidence-based detector initialized!")
        
        # Test with realistic crypto price movements
        test_cases = [
            {
                'name': 'BTC Small Movement (Below Tier 3)',
                'product_id': 'BTC-USD',
                'price_changes': [43000, 43050],  # 0.116% change - below 0.15% tier 3
                'expected_events': 0
            },
            {
                'name': 'BTC Tier 3 Movement',
                'product_id': 'BTC-USD', 
                'price_changes': [43000, 43070],  # 0.163% change - above 0.15% tier 3
                'expected_events': 1
            },
            {
                'name': 'ETH Tier 2 Movement',
                'product_id': 'ETH-USD',
                'price_changes': [2400, 2410],  # 0.417% change - above 0.39% tier 2
                'expected_events': 1
            },
            {
                'name': 'SOL Tier 1 Movement',
                'product_id': 'SOL-USD',
                'price_changes': [100, 101],  # 1.0% change - above 0.90% tier 1
                'expected_events': 1
            }
        ]
        
        total_events = 0
        
        for test_case in test_cases:
            print(f"\n🔍 Testing: {test_case['name']}")
            
            # Simulate price movement
            prices = test_case['price_changes']
            product_id = test_case['product_id']
            
            # First price point (establish baseline)
            ticker_data_1 = {
                'price': prices[0],
                'volume_24h': 1000000
            }
            events_1 = detector.process_ticker_data(product_id, ticker_data_1)
            
            # Small delay to simulate time passage
            time.sleep(0.1)
            
            # Second price point (trigger detection)
            ticker_data_2 = {
                'price': prices[1], 
                'volume_24h': 1000000
            }
            events_2 = detector.process_ticker_data(product_id, ticker_data_2)
            
            # Calculate actual price change
            price_change = abs((prices[1] - prices[0]) / prices[0]) * 100
            
            print(f"   📈 Price change: {price_change:.3f}%")
            print(f"   🎯 Events detected: {len(events_2)}")
            print(f"   ✅ Expected events: {test_case['expected_events']}")
            
            if len(events_2) == test_case['expected_events']:
                print(f"   ✅ PASS - Correct event detection!")
            else:
                print(f"   ❌ FAIL - Expected {test_case['expected_events']}, got {len(events_2)}")
            
            if events_2:
                event = events_2[0]
                print(f"   📊 Event details:")
                print(f"      - Type: {event.event_type.value}")
                print(f"      - Urgency: {event.urgency.value}")
                print(f"      - Confidence: {event.confidence:.2f}")
                print(f"      - Tier: {event.data.get('tier', 'N/A')}")
                
            total_events += len(events_2)
        
        # Test performance metrics
        metrics = detector.get_performance_metrics()
        print(f"\n📊 Performance Metrics:")
        print(f"   - Detector type: {metrics['detector_type']}")
        print(f"   - Assets monitored: {metrics['assets_monitored']}")
        print(f"   - Active assets: {metrics['active_assets']}")
        print(f"   - Total events: {total_events}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_threshold_values():
    """Test that threshold values are realistic for crypto markets."""
    try:
        print("\n🎯 Testing Threshold Values...")
        
        from agents.streaming.evidence_based_event_detector import EvidenceBasedEventDetector
        
        detector = EvidenceBasedEventDetector()
        
        # Check BTC thresholds
        btc_thresholds = detector.asset_thresholds['BTC-USD']
        print(f"📊 BTC Thresholds:")
        print(f"   - Tier 3 (Notice): {btc_thresholds.tier_3_price:.3f}%")
        print(f"   - Tier 2 (Warning): {btc_thresholds.tier_2_price:.3f}%") 
        print(f"   - Tier 1 (Alert): {btc_thresholds.tier_1_price:.3f}%")
        
        # Validate thresholds are reasonable
        assert 0.1 <= btc_thresholds.tier_3_price <= 0.5, "BTC Tier 3 threshold unrealistic"
        assert 0.2 <= btc_thresholds.tier_2_price <= 1.0, "BTC Tier 2 threshold unrealistic"
        assert 0.3 <= btc_thresholds.tier_1_price <= 2.0, "BTC Tier 1 threshold unrealistic"
        
        print("✅ All threshold values are realistic for crypto markets!")
        return True
        
    except Exception as e:
        print(f"❌ Threshold validation failed: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Evidence-Based Threshold Validation Test")
    print("=" * 60)
    
    # Test 1: Evidence-based detector functionality
    detector_success = test_evidence_based_detector()
    
    # Test 2: Threshold value validation
    threshold_success = test_threshold_values()
    
    print("\n📊 Test Results:")
    print(f"  Evidence-Based Detector: {'✅ PASS' if detector_success else '❌ FAIL'}")
    print(f"  Threshold Validation:    {'✅ PASS' if threshold_success else '❌ FAIL'}")
    
    if detector_success and threshold_success:
        print("\n🎯 Evidence-based thresholds have been IMPLEMENTED!")
        print("   Ready to proceed with real-time pipeline testing.")
    else:
        print("\n❌ Evidence-based threshold implementation has issues!")
        print("   Must fix before proceeding.")
