"""
Performance Optimizer for M1 Max with 64GB RAM
==============================================

Advanced performance optimization system specifically designed for Apple M1 Max
with 64GB RAM, leveraging GPU acceleration, memory management, and parallel processing.
"""

import asyncio
import psutil
import platform
import multiprocessing
import concurrent.futures
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from datetime import datetime
import structlog

logger = structlog.get_logger(__name__)


@dataclass
class SystemSpecs:
    """System specifications for optimization."""
    cpu_count: int
    memory_gb: float
    is_m1_max: bool
    gpu_available: bool
    max_workers: int


@dataclass
class PerformanceMetrics:
    """Performance monitoring metrics."""
    cpu_usage: float
    memory_usage_gb: float
    memory_usage_percent: float
    active_tasks: int
    completed_tasks: int
    average_task_time: float
    gpu_utilization: float = 0.0


class M1MaxPerformanceOptimizer:
    """Performance optimizer specifically designed for M1 Max with 64GB RAM."""
    
    def __init__(self):
        self.system_specs = self._detect_system_specs()
        self.task_executor = None
        self.memory_pool = None
        self.performance_metrics = PerformanceMetrics(0, 0, 0, 0, 0, 0)
        
        # M1 Max specific optimizations
        self.gpu_acceleration_enabled = self._setup_gpu_acceleration()
        self.memory_management_enabled = self._setup_memory_management()
        
        logger.info("M1 Max Performance Optimizer initialized", 
                   cpu_count=self.system_specs.cpu_count,
                   memory_gb=self.system_specs.memory_gb,
                   is_m1_max=self.system_specs.is_m1_max,
                   gpu_enabled=self.gpu_acceleration_enabled)
    
    def _detect_system_specs(self) -> SystemSpecs:
        """Detect system specifications for optimization."""
        cpu_count = multiprocessing.cpu_count()
        memory_bytes = psutil.virtual_memory().total
        memory_gb = memory_bytes / (1024**3)
        
        # Detect M1 Max
        is_m1_max = (
            platform.system() == "Darwin" and 
            platform.processor() == "arm" and
            memory_gb >= 32  # M1 Max typically has 32GB or 64GB
        )
        
        # Optimize worker count for M1 Max
        if is_m1_max:
            # M1 Max has 10 cores (8 performance + 2 efficiency)
            # Use 8 workers for CPU-intensive tasks to leave headroom
            max_workers = min(8, cpu_count - 2)
        else:
            max_workers = min(cpu_count - 1, 16)
        
        return SystemSpecs(
            cpu_count=cpu_count,
            memory_gb=memory_gb,
            is_m1_max=is_m1_max,
            gpu_available=is_m1_max,  # M1 Max has integrated GPU
            max_workers=max_workers
        )
    
    def _setup_gpu_acceleration(self) -> bool:
        """Setup GPU acceleration for M1 Max."""
        if not self.system_specs.is_m1_max:
            return False
        
        try:
            # Check for Metal Performance Shaders (MPS) support
            import torch
            if hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
                logger.info("M1 Max GPU acceleration enabled via MPS")
                return True
        except ImportError:
            logger.info("PyTorch not available, GPU acceleration disabled")
        
        return False
    
    def _setup_memory_management(self) -> bool:
        """Setup advanced memory management for 64GB RAM."""
        try:
            # Configure memory pool for large datasets
            if self.system_specs.memory_gb >= 32:
                # Use up to 50% of available memory for caching
                cache_size_gb = min(self.system_specs.memory_gb * 0.5, 32)
                logger.info(f"Memory management enabled with {cache_size_gb:.1f}GB cache")
                return True
        except Exception as e:
            logger.warning(f"Memory management setup failed: {e}")
        
        return False
    
    async def optimize_concurrent_execution(self, 
                                          tasks: List[Callable], 
                                          max_concurrent: Optional[int] = None) -> List[Any]:
        """Execute tasks with optimal concurrency for M1 Max."""
        if max_concurrent is None:
            max_concurrent = self.system_specs.max_workers
        
        # Use semaphore to limit concurrent execution
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def execute_with_semaphore(task):
            async with semaphore:
                start_time = asyncio.get_event_loop().time()
                try:
                    if asyncio.iscoroutinefunction(task):
                        result = await task()
                    else:
                        # Run CPU-bound tasks in thread pool
                        result = await asyncio.get_event_loop().run_in_executor(
                            self.task_executor, task
                        )
                    
                    execution_time = asyncio.get_event_loop().time() - start_time
                    self._update_performance_metrics(execution_time, success=True)
                    return result
                    
                except Exception as e:
                    execution_time = asyncio.get_event_loop().time() - start_time
                    self._update_performance_metrics(execution_time, success=False)
                    logger.error(f"Task execution failed: {e}")
                    raise
        
        # Execute all tasks concurrently
        results = await asyncio.gather(
            *[execute_with_semaphore(task) for task in tasks],
            return_exceptions=True
        )
        
        return results
    
    def optimize_memory_usage(self, data_size_mb: float) -> Dict[str, Any]:
        """Optimize memory usage based on data size and available RAM."""
        available_memory_gb = self.system_specs.memory_gb
        data_size_gb = data_size_mb / 1024
        
        # Memory optimization strategy
        if data_size_gb < available_memory_gb * 0.1:
            # Small dataset - load everything in memory
            strategy = "full_memory"
            batch_size = None
            use_streaming = False
        elif data_size_gb < available_memory_gb * 0.5:
            # Medium dataset - use batching
            strategy = "batched"
            batch_size = max(1000, int(50000 * (available_memory_gb / 64)))
            use_streaming = False
        else:
            # Large dataset - use streaming
            strategy = "streaming"
            batch_size = max(100, int(5000 * (available_memory_gb / 64)))
            use_streaming = True
        
        return {
            "strategy": strategy,
            "batch_size": batch_size,
            "use_streaming": use_streaming,
            "memory_limit_gb": available_memory_gb * 0.8,  # Use max 80% of RAM
            "recommended_workers": self.system_specs.max_workers
        }
    
    def _update_performance_metrics(self, execution_time: float, success: bool):
        """Update performance metrics."""
        if success:
            self.performance_metrics.completed_tasks += 1
            
            # Update average task time
            total_time = (self.performance_metrics.average_task_time * 
                         (self.performance_metrics.completed_tasks - 1) + execution_time)
            self.performance_metrics.average_task_time = total_time / self.performance_metrics.completed_tasks
        
        # Update system metrics
        self.performance_metrics.cpu_usage = psutil.cpu_percent()
        memory = psutil.virtual_memory()
        self.performance_metrics.memory_usage_gb = (memory.total - memory.available) / (1024**3)
        self.performance_metrics.memory_usage_percent = memory.percent
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report."""
        return {
            "system_specs": {
                "cpu_count": self.system_specs.cpu_count,
                "memory_gb": self.system_specs.memory_gb,
                "is_m1_max": self.system_specs.is_m1_max,
                "max_workers": self.system_specs.max_workers
            },
            "current_metrics": {
                "cpu_usage": self.performance_metrics.cpu_usage,
                "memory_usage_gb": self.performance_metrics.memory_usage_gb,
                "memory_usage_percent": self.performance_metrics.memory_usage_percent,
                "completed_tasks": self.performance_metrics.completed_tasks,
                "average_task_time": self.performance_metrics.average_task_time
            },
            "optimizations": {
                "gpu_acceleration": self.gpu_acceleration_enabled,
                "memory_management": self.memory_management_enabled,
                "concurrent_workers": self.system_specs.max_workers
            },
            "recommendations": self._get_optimization_recommendations()
        }
    
    def _get_optimization_recommendations(self) -> List[str]:
        """Get optimization recommendations based on current performance."""
        recommendations = []
        
        if self.performance_metrics.cpu_usage > 80:
            recommendations.append("High CPU usage detected - consider reducing concurrent tasks")
        
        if self.performance_metrics.memory_usage_percent > 80:
            recommendations.append("High memory usage detected - enable streaming for large datasets")
        
        if self.performance_metrics.average_task_time > 30:
            recommendations.append("Long task execution times - consider task optimization or GPU acceleration")
        
        if not self.gpu_acceleration_enabled and self.system_specs.is_m1_max:
            recommendations.append("GPU acceleration not enabled - install PyTorch with MPS support")
        
        return recommendations
    
    async def __aenter__(self):
        """Async context manager entry."""
        # Initialize thread pool executor optimized for M1 Max
        self.task_executor = concurrent.futures.ThreadPoolExecutor(
            max_workers=self.system_specs.max_workers,
            thread_name_prefix="M1MaxOptimizer"
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.task_executor:
            self.task_executor.shutdown(wait=True)


# Global optimizer instance
performance_optimizer = M1MaxPerformanceOptimizer()
