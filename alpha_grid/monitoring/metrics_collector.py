"""
Metrics Collector for Alpha Grid Monitoring
==========================================

Comprehensive metrics collection system for monitoring the Alpha Grid
cryptocurrency agentic analysis system with Prometheus integration.
"""

import time
import psutil
import asyncio
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from datetime import datetime
from prometheus_client import Counter, Histogram, Gauge, start_http_server
import structlog

logger = structlog.get_logger(__name__)


@dataclass
class SystemMetrics:
    """System performance metrics."""
    cpu_usage_percent: float = 0.0
    memory_usage_gb: float = 0.0
    memory_available_gb: float = 0.0
    active_connections: int = 0
    uptime_seconds: float = 0.0


@dataclass
class AgentMetrics:
    """AI agent performance metrics."""
    total_analyses: int = 0
    successful_analyses: int = 0
    failed_analyses: int = 0
    average_analysis_time: float = 0.0
    consensus_formation_time: float = 0.0
    consensus_confidence: float = 0.0


@dataclass
class EventMetrics:
    """Event detection metrics."""
    total_events: int = 0
    tier1_events: int = 0
    tier2_events: int = 0
    tier3_events: int = 0
    events_per_minute: float = 0.0
    detection_latency: float = 0.0


class AlphaGridMetricsCollector:
    """Comprehensive metrics collector for Alpha Grid system."""
    
    def __init__(self, port: int = 8000):
        self.port = port
        self.start_time = time.time()
        
        # Prometheus metrics
        self._setup_prometheus_metrics()
        
        # Internal metrics storage
        self.system_metrics = SystemMetrics()
        self.agent_metrics = AgentMetrics()
        self.event_metrics = EventMetrics()
        
        # Metrics collection state
        self.is_collecting = False
        self.collection_interval = 5.0  # seconds
        
        logger.info("Alpha Grid Metrics Collector initialized", port=port)
    
    def _setup_prometheus_metrics(self):
        """Setup Prometheus metrics."""
        # System metrics
        self.system_status = Gauge('alpha_grid_system_status', 'Overall system status (0-1)')
        self.websocket_connected = Gauge('alpha_grid_websocket_connected', 'WebSocket connection status')
        self.memory_usage = Gauge('alpha_grid_memory_usage_bytes', 'Memory usage in bytes')
        self.memory_available = Gauge('alpha_grid_memory_available_bytes', 'Available memory in bytes')
        self.cpu_usage = Gauge('alpha_grid_cpu_usage_percent', 'CPU usage percentage')
        self.cpu_cores_active = Gauge('alpha_grid_cpu_cores_active', 'Number of active CPU cores')
        
        # Event detection metrics
        self.events_detected = Counter('alpha_grid_events_detected_total', 'Total events detected', ['asset', 'tier'])
        self.events_tier1 = Counter('alpha_grid_events_tier1_total', 'Tier 1 events detected', ['asset'])
        self.events_tier2 = Counter('alpha_grid_events_tier2_total', 'Tier 2 events detected', ['asset'])
        self.events_tier3 = Counter('alpha_grid_events_tier3_total', 'Tier 3 events detected', ['asset'])
        
        # Agent performance metrics
        self.agent_analyses = Counter('alpha_grid_agent_analyses_total', 'Total agent analyses', ['agent_type', 'status'])
        self.agent_analysis_duration = Histogram('alpha_grid_agent_analysis_duration_seconds', 'Agent analysis duration')
        self.agent_success_rate = Gauge('alpha_grid_agent_success_rate', 'Agent analysis success rate')
        
        # Consensus metrics
        self.consensus_formation_time = Histogram('alpha_grid_consensus_formation_time_seconds', 'Consensus formation time')
        self.consensus_confidence = Gauge('alpha_grid_consensus_confidence_score', 'Consensus confidence score')
        
        # Error and reliability metrics
        self.errors_total = Counter('alpha_grid_errors_total', 'Total errors', ['component', 'error_type'])
        self.circuit_breaker_open = Gauge('alpha_grid_circuit_breaker_open', 'Circuit breaker status', ['component'])
        
        # WebSocket metrics
        self.websocket_messages = Counter('alpha_grid_websocket_messages_total', 'WebSocket messages received', ['product'])
        self.data_processing_latency = Histogram('alpha_grid_data_processing_latency_seconds', 'Data processing latency')
    
    async def start_collection(self):
        """Start metrics collection."""
        if self.is_collecting:
            logger.warning("Metrics collection already running")
            return
        
        self.is_collecting = True
        
        # Start Prometheus HTTP server
        try:
            start_http_server(self.port)
            logger.info(f"Prometheus metrics server started on port {self.port}")
        except Exception as e:
            logger.error(f"Failed to start Prometheus server: {e}")
            return
        
        # Start metrics collection loop
        asyncio.create_task(self._collection_loop())
        logger.info("Metrics collection started")
    
    async def stop_collection(self):
        """Stop metrics collection."""
        self.is_collecting = False
        logger.info("Metrics collection stopped")
    
    async def _collection_loop(self):
        """Main metrics collection loop."""
        while self.is_collecting:
            try:
                await self._collect_system_metrics()
                await asyncio.sleep(self.collection_interval)
            except Exception as e:
                logger.error(f"Error in metrics collection: {e}")
                await asyncio.sleep(self.collection_interval)
    
    async def _collect_system_metrics(self):
        """Collect system performance metrics."""
        try:
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            
            self.cpu_usage.set(cpu_percent)
            self.cpu_cores_active.set(cpu_count)
            
            # Memory metrics
            memory = psutil.virtual_memory()
            self.memory_usage.set(memory.used)
            self.memory_available.set(memory.available)
            
            # Update internal metrics
            self.system_metrics.cpu_usage_percent = cpu_percent
            self.system_metrics.memory_usage_gb = memory.used / (1024**3)
            self.system_metrics.memory_available_gb = memory.available / (1024**3)
            self.system_metrics.uptime_seconds = time.time() - self.start_time
            
            # System status (composite metric)
            system_health = self._calculate_system_health()
            self.system_status.set(system_health)
            
        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")
    
    def _calculate_system_health(self) -> float:
        """Calculate overall system health score (0-1)."""
        health_score = 1.0
        
        # Penalize high CPU usage
        if self.system_metrics.cpu_usage_percent > 80:
            health_score -= 0.3
        elif self.system_metrics.cpu_usage_percent > 60:
            health_score -= 0.1
        
        # Penalize high memory usage
        memory_usage_percent = (self.system_metrics.memory_usage_gb / 
                               (self.system_metrics.memory_usage_gb + self.system_metrics.memory_available_gb)) * 100
        
        if memory_usage_percent > 90:
            health_score -= 0.4
        elif memory_usage_percent > 75:
            health_score -= 0.2
        
        # Penalize high error rates
        if hasattr(self, '_recent_error_count') and self._recent_error_count > 10:
            health_score -= 0.3
        
        return max(0.0, health_score)
    
    def record_event_detected(self, asset: str, tier: int, event_type: str):
        """Record an event detection."""
        self.events_detected.labels(asset=asset, tier=f"tier{tier}").inc()
        
        if tier == 1:
            self.events_tier1.labels(asset=asset).inc()
        elif tier == 2:
            self.events_tier2.labels(asset=asset).inc()
        elif tier == 3:
            self.events_tier3.labels(asset=asset).inc()
        
        self.event_metrics.total_events += 1
        setattr(self.event_metrics, f'tier{tier}_events', 
                getattr(self.event_metrics, f'tier{tier}_events') + 1)
    
    def record_agent_analysis(self, agent_type: str, duration: float, success: bool):
        """Record an agent analysis."""
        status = "success" if success else "failure"
        self.agent_analyses.labels(agent_type=agent_type, status=status).inc()
        self.agent_analysis_duration.observe(duration)
        
        # Update internal metrics
        self.agent_metrics.total_analyses += 1
        if success:
            self.agent_metrics.successful_analyses += 1
        else:
            self.agent_metrics.failed_analyses += 1
        
        # Update success rate
        success_rate = self.agent_metrics.successful_analyses / self.agent_metrics.total_analyses
        self.agent_success_rate.set(success_rate)
        
        # Update average analysis time
        total_time = self.agent_metrics.average_analysis_time * (self.agent_metrics.total_analyses - 1) + duration
        self.agent_metrics.average_analysis_time = total_time / self.agent_metrics.total_analyses
    
    def record_consensus_formation(self, duration: float, confidence: float):
        """Record consensus formation metrics."""
        self.consensus_formation_time.observe(duration)
        self.consensus_confidence.set(confidence)
        
        self.agent_metrics.consensus_formation_time = duration
        self.agent_metrics.consensus_confidence = confidence
    
    def record_websocket_message(self, product: str):
        """Record WebSocket message received."""
        self.websocket_messages.labels(product=product).inc()
    
    def record_websocket_status(self, connected: bool):
        """Record WebSocket connection status."""
        self.websocket_connected.set(1 if connected else 0)
    
    def record_error(self, component: str, error_type: str):
        """Record an error occurrence."""
        self.errors_total.labels(component=component, error_type=error_type).inc()
        
        # Track recent errors for health calculation
        if not hasattr(self, '_recent_error_count'):
            self._recent_error_count = 0
        self._recent_error_count += 1
    
    def record_circuit_breaker_status(self, component: str, is_open: bool):
        """Record circuit breaker status."""
        self.circuit_breaker_open.labels(component=component).set(1 if is_open else 0)
    
    def record_data_processing_latency(self, latency: float):
        """Record data processing latency."""
        self.data_processing_latency.observe(latency)
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get comprehensive metrics summary."""
        return {
            "system": {
                "cpu_usage_percent": self.system_metrics.cpu_usage_percent,
                "memory_usage_gb": self.system_metrics.memory_usage_gb,
                "memory_available_gb": self.system_metrics.memory_available_gb,
                "uptime_seconds": self.system_metrics.uptime_seconds,
                "health_score": self._calculate_system_health()
            },
            "agents": {
                "total_analyses": self.agent_metrics.total_analyses,
                "successful_analyses": self.agent_metrics.successful_analyses,
                "failed_analyses": self.agent_metrics.failed_analyses,
                "success_rate": (self.agent_metrics.successful_analyses / 
                               max(1, self.agent_metrics.total_analyses)),
                "average_analysis_time": self.agent_metrics.average_analysis_time,
                "consensus_formation_time": self.agent_metrics.consensus_formation_time,
                "consensus_confidence": self.agent_metrics.consensus_confidence
            },
            "events": {
                "total_events": self.event_metrics.total_events,
                "tier1_events": self.event_metrics.tier1_events,
                "tier2_events": self.event_metrics.tier2_events,
                "tier3_events": self.event_metrics.tier3_events
            },
            "collection": {
                "is_collecting": self.is_collecting,
                "collection_interval": self.collection_interval,
                "prometheus_port": self.port
            }
        }


# Global metrics collector instance
metrics_collector = AlphaGridMetricsCollector()


class AlertManager:
    """Alert management system for Alpha Grid monitoring."""

    def __init__(self, metrics_collector: AlphaGridMetricsCollector):
        self.metrics_collector = metrics_collector
        self.alert_rules = self._setup_alert_rules()
        self.active_alerts = {}

    def _setup_alert_rules(self) -> Dict[str, Dict[str, Any]]:
        """Setup alert rules for system monitoring."""
        return {
            "high_cpu_usage": {
                "condition": lambda: self.metrics_collector.system_metrics.cpu_usage_percent > 85,
                "severity": "warning",
                "message": "High CPU usage detected",
                "cooldown": 300  # 5 minutes
            },
            "high_memory_usage": {
                "condition": lambda: self.metrics_collector.system_metrics.memory_usage_gb > 50,
                "severity": "warning",
                "message": "High memory usage detected",
                "cooldown": 300
            },
            "system_health_critical": {
                "condition": lambda: self.metrics_collector._calculate_system_health() < 0.5,
                "severity": "critical",
                "message": "System health critically low",
                "cooldown": 60
            },
            "agent_failure_rate_high": {
                "condition": lambda: (
                    self.metrics_collector.agent_metrics.total_analyses > 10 and
                    (self.metrics_collector.agent_metrics.failed_analyses /
                     self.metrics_collector.agent_metrics.total_analyses) > 0.3
                ),
                "severity": "critical",
                "message": "Agent failure rate exceeds 30%",
                "cooldown": 180
            },
            "no_events_detected": {
                "condition": lambda: (
                    time.time() - self.metrics_collector.start_time > 600 and  # After 10 minutes
                    self.metrics_collector.event_metrics.total_events == 0
                ),
                "severity": "warning",
                "message": "No events detected in extended period",
                "cooldown": 600
            }
        }

    async def check_alerts(self):
        """Check all alert conditions and trigger alerts if needed."""
        current_time = time.time()

        for alert_name, rule in self.alert_rules.items():
            try:
                if rule["condition"]():
                    # Check if alert is in cooldown
                    if (alert_name in self.active_alerts and
                        current_time - self.active_alerts[alert_name]["last_triggered"] < rule["cooldown"]):
                        continue

                    # Trigger alert
                    await self._trigger_alert(alert_name, rule)

                elif alert_name in self.active_alerts:
                    # Clear resolved alert
                    await self._clear_alert(alert_name)

            except Exception as e:
                logger.error(f"Error checking alert {alert_name}: {e}")

    async def _trigger_alert(self, alert_name: str, rule: Dict[str, Any]):
        """Trigger an alert."""
        current_time = time.time()

        alert_info = {
            "name": alert_name,
            "severity": rule["severity"],
            "message": rule["message"],
            "triggered_at": current_time,
            "last_triggered": current_time
        }

        self.active_alerts[alert_name] = alert_info

        logger.warning(f"ALERT TRIGGERED: {alert_name}",
                      severity=rule["severity"],
                      message=rule["message"])

        # Here you could integrate with external alerting systems
        # like Slack, PagerDuty, email, etc.

    async def _clear_alert(self, alert_name: str):
        """Clear a resolved alert."""
        if alert_name in self.active_alerts:
            del self.active_alerts[alert_name]
            logger.info(f"ALERT CLEARED: {alert_name}")


# Global alert manager
alert_manager = AlertManager(metrics_collector)
