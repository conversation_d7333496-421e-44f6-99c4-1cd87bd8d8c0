{"dashboard": {"id": null, "title": "Alpha Grid Crypto Agentic System", "tags": ["alpha-grid", "crypto", "agentic", "real-time"], "timezone": "browser", "panels": [{"id": 1, "title": "System Overview", "type": "stat", "targets": [{"expr": "alpha_grid_system_status", "legendFormat": "System Status"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 0.5}, {"color": "green", "value": 0.8}]}}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "WebSocket Connection Status", "type": "stat", "targets": [{"expr": "alpha_grid_websocket_connected", "legendFormat": "WebSocket Connected"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "Events Detected (Real-time)", "type": "graph", "targets": [{"expr": "rate(alpha_grid_events_detected_total[5m])", "legendFormat": "Events/min"}], "yAxes": [{"label": "Events per minute", "min": 0}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 8}}, {"id": 4, "title": "Event Detection by Tier", "type": "graph", "targets": [{"expr": "rate(alpha_grid_events_tier1_total[5m])", "legendFormat": "Tier 1 (Critical)"}, {"expr": "rate(alpha_grid_events_tier2_total[5m])", "legendFormat": "Tier 2 (High)"}, {"expr": "rate(alpha_grid_events_tier3_total[5m])", "legendFormat": "Tier 3 (Medium)"}], "yAxes": [{"label": "Events per minute", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 5, "title": "AI Agent Performance", "type": "graph", "targets": [{"expr": "alpha_grid_agent_analysis_duration_seconds", "legendFormat": "Analysis Duration"}, {"expr": "alpha_grid_agent_success_rate", "legendFormat": "Success Rate"}], "yAxes": [{"label": "Seconds / Rate", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 6, "title": "Memory Usage (M1 Max Optimized)", "type": "graph", "targets": [{"expr": "alpha_grid_memory_usage_bytes / 1024 / 1024 / 1024", "legendFormat": "Memory Usage (GB)"}, {"expr": "alpha_grid_memory_available_bytes / 1024 / 1024 / 1024", "legendFormat": "Available Memory (GB)"}], "yAxes": [{"label": "Gigabytes", "min": 0, "max": 64}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}}, {"id": 7, "title": "CPU Usage (M1 Max Cores)", "type": "graph", "targets": [{"expr": "alpha_grid_cpu_usage_percent", "legendFormat": "CPU Usage %"}, {"expr": "alpha_grid_cpu_cores_active", "legendFormat": "Active Cores"}], "yAxes": [{"label": "Percent / Cores", "min": 0, "max": 100}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}}, {"id": 8, "title": "Consensus Formation Metrics", "type": "graph", "targets": [{"expr": "alpha_grid_consensus_formation_time_seconds", "legendFormat": "Consensus Time"}, {"expr": "alpha_grid_consensus_confidence_score", "legendFormat": "Confidence Score"}], "yAxes": [{"label": "Seconds / Score", "min": 0}], "gridPos": {"h": 8, "w": 24, "x": 0, "y": 32}}, {"id": 9, "title": "Error Rates and Circuit Breaker Status", "type": "graph", "targets": [{"expr": "rate(alpha_grid_errors_total[5m])", "legendFormat": "Error Rate"}, {"expr": "alpha_grid_circuit_breaker_open", "legendFormat": "Circuit Breaker Open"}], "yAxes": [{"label": "Errors/min", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 40}}, {"id": 10, "title": "Real-time Data Throughput", "type": "graph", "targets": [{"expr": "rate(alpha_grid_websocket_messages_total[1m])", "legendFormat": "Messages/min"}, {"expr": "alpha_grid_data_processing_latency_seconds", "legendFormat": "Processing Latency"}], "yAxes": [{"label": "Messages/min | Seconds", "min": 0}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 40}}], "time": {"from": "now-1h", "to": "now"}, "refresh": "5s", "schemaVersion": 27, "version": 1, "annotations": {"list": [{"name": "Critical Events", "datasource": "prometheus", "enable": true, "expr": "alpha_grid_events_tier1_total", "iconColor": "red", "titleFormat": "Tier 1 Event Detected"}]}, "templating": {"list": [{"name": "asset", "type": "query", "query": "label_values(alpha_grid_events_detected_total, asset)", "refresh": 1, "includeAll": true, "multi": true}]}}}