"""
Base AI agent framework for Alpha Grid cryptocurrency analytics.

This module provides the foundation for AI-powered analysis agents using
DeepSeek R1 for reasoning and decision making.
"""

import asyncio
import time
import uuid
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
import logging
import ollama

from core.database import db_manager
from core.models import (
    AgentAnalysis, AgentType, DataSource, CryptoAsset, MarketData
)
from alpha_grid.core.config import config_manager


logger = logging.getLogger(__name__)


class PromptTemplate:
    """Template system for AI prompts."""
    
    SYSTEM_PROMPT = """You are an expert cryptocurrency analyst specializing in {agent_type} analysis.
Your role is to analyze cryptocurrency data and provide objective, data-driven insights.

Key responsibilities:
- Analyze provided data with deep reasoning
- Generate numerical scores (0-100) based on evidence
- Provide clear, logical reasoning for your conclusions
- Identify key factors influencing your analysis
- Maintain objectivity and avoid speculation

Response format:
- Score: [0-100 numerical score]
- Confidence: [0.0-1.0 confidence level]
- Reasoning: [Detailed explanation of your analysis]
- Key Factors: [List of 3-5 most important factors]
"""
    
    ANALYSIS_PROMPT = """Analyze the following cryptocurrency data for {symbol} ({name}):

MARKET DATA:
{market_data}

HISTORICAL CONTEXT:
{historical_context}

SPECIFIC FOCUS ({agent_type}):
{focus_areas}

Please provide your analysis following this exact format:
Score: [numerical value 0-100]
Confidence: [decimal value 0.0-1.0]
Reasoning: [your detailed analysis]
Key Factors: [factor1, factor2, factor3, factor4, factor5]
"""
    
    @classmethod
    def build_system_prompt(cls, agent_type: str) -> str:
        """Build system prompt for specific agent type."""
        return cls.SYSTEM_PROMPT.format(agent_type=agent_type)
    
    @classmethod
    def build_analysis_prompt(
        cls,
        symbol: str,
        name: str,
        agent_type: str,
        market_data: str,
        historical_context: str,
        focus_areas: str
    ) -> str:
        """Build analysis prompt with data."""
        return cls.ANALYSIS_PROMPT.format(
            symbol=symbol,
            name=name,
            agent_type=agent_type,
            market_data=market_data,
            historical_context=historical_context,
            focus_areas=focus_areas
        )


class LLMClient:
    """Client for DeepSeek R1 via Ollama."""

    def __init__(self, model: str = "deepseek-r1:latest"):
        self.model = model
        self.logger = logger
        # Ensure we connect to the correct Ollama port
        self.client = ollama.Client(host='http://localhost:11434')
    
    async def generate_analysis(
        self,
        system_prompt: str,
        user_prompt: str,
        temperature: float = 0.3
    ) -> Dict[str, Any]:
        """Generate analysis using DeepSeek R1."""
        try:
            start_time = time.time()
            
            # Prepare messages
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            # Generate response with proven stable token limit using explicit client
            response = self.client.chat(
                model=self.model,
                messages=messages,
                options={
                    "temperature": temperature,
                    "top_p": 0.9,
                    "max_tokens": 2000  # Proven stable limit from original working configuration
                }
            )
            
            execution_time = time.time() - start_time
            content = response["message"]["content"]
            
            # Parse response
            parsed = self._parse_response(content)
            parsed["execution_time"] = execution_time
            parsed["raw_response"] = content
            
            self.logger.debug(f"LLM analysis generated in {execution_time:.2f}s")
            return parsed
            
        except Exception as e:
            self.logger.error(f"Failed to generate LLM analysis: {str(e)}")
            return {
                "score": 50.0,
                "confidence": 0.1,
                "reasoning": f"Analysis failed: {str(e)}",
                "key_factors": ["error"],
                "execution_time": 0.0,
                "error": str(e)
            }
    
    def _parse_response(self, content: str) -> Dict[str, Any]:
        """Parse LLM response into structured data, handling DeepSeek R1 reasoning format."""
        try:
            # Handle DeepSeek R1's <think> tags - extract final answer after thinking
            if "<think>" in content and "</think>" in content:
                # Extract content after </think> tag
                think_end = content.find("</think>")
                if think_end != -1:
                    final_content = content[think_end + 8:].strip()
                    if final_content:
                        content = final_content

            lines = content.strip().split('\n')
            result = {
                "score": 50.0,
                "confidence": 0.5,
                "reasoning": "",
                "key_factors": []
            }

            # Track if we're collecting multi-line reasoning
            collecting_reasoning = False
            reasoning_lines = []

            for line in lines:
                line = line.strip()

                if line.startswith("Score:"):
                    try:
                        score_str = line.replace("Score:", "").strip()
                        result["score"] = max(0.0, min(100.0, float(score_str)))
                    except ValueError:
                        pass

                elif line.startswith("Confidence:"):
                    try:
                        conf_str = line.replace("Confidence:", "").strip()
                        result["confidence"] = max(0.0, min(1.0, float(conf_str)))
                    except ValueError:
                        pass

                elif line.startswith("Reasoning:"):
                    reasoning_text = line.replace("Reasoning:", "").strip()
                    if reasoning_text:
                        reasoning_lines.append(reasoning_text)
                    collecting_reasoning = True

                elif line.startswith("Key Factors:"):
                    collecting_reasoning = False
                    factors_str = line.replace("Key Factors:", "").strip()
                    # Parse comma-separated factors or bracket format
                    if factors_str.startswith('[') and factors_str.endswith(']'):
                        factors_str = factors_str[1:-1]
                    factors = [f.strip() for f in factors_str.split(',') if f.strip()]
                    result["key_factors"] = factors[:5]  # Limit to 5 factors

                elif collecting_reasoning and line and not line.startswith(("Score:", "Confidence:", "Key Factors:")):
                    # Continue collecting reasoning text
                    reasoning_lines.append(line)

            # Join reasoning lines
            if reasoning_lines:
                result["reasoning"] = " ".join(reasoning_lines)

            # Ensure reasoning is captured even if not properly formatted
            if not result["reasoning"] and content:
                # Clean content of think tags and use as fallback - NO TRUNCATION for full context
                clean_content = content.replace("<think>", "").replace("</think>", "").strip()
                result["reasoning"] = clean_content  # Full content without truncation

            return result

        except Exception as e:
            logger.error(f"Failed to parse LLM response: {str(e)}")
            return {
                "score": 50.0,
                "confidence": 0.1,
                "reasoning": f"Parse error: {str(e)}",
                "key_factors": ["parse_error"],
            }


class BaseIntelligenceAgent(ABC):
    """Base class for AI-powered cryptocurrency analysis agents."""
    
    def __init__(self, agent_type: AgentType, data_sources: List[DataSource]):
        """Initialize base intelligence agent."""
        self.agent_type = agent_type
        self.data_sources = data_sources
        self.llm_client = LLMClient()
        self.logger = logger
        
        # Performance metrics
        self.analysis_count = 0
        self.total_execution_time = 0.0
        self.error_count = 0
    
    async def analyze(self, asset: CryptoAsset, max_retries: int = 2) -> AgentAnalysis:
        """Perform AI-powered analysis with enhanced error handling and retry logic."""
        analysis_id = str(uuid.uuid4())
        start_time = time.time()

        for attempt in range(max_retries + 1):
            try:
                self.logger.info(f"Starting analysis for {asset.symbol} (attempt {attempt + 1}/{max_retries + 1})")

                # Phase 1: Data gathering with timeout and fallbacks
                market_data = await self._gather_market_data_with_fallback(asset.id)
                historical_context = await self._gather_historical_context_with_fallback(asset.id)

                # Phase 2: Prompt building with validation
                system_prompt = PromptTemplate.build_system_prompt(self.agent_type.value)
                focus_areas = self._get_focus_areas()

                analysis_prompt = PromptTemplate.build_analysis_prompt(
                    symbol=asset.symbol,
                    name=asset.name,
                    agent_type=self.agent_type.value,
                    market_data=self._format_market_data(market_data),
                    historical_context=historical_context,
                    focus_areas=focus_areas
                )

                # Phase 3: LLM analysis with timeout and circuit breaker
                llm_result = await self._generate_analysis_with_timeout(
                    system_prompt=system_prompt,
                    user_prompt=analysis_prompt,
                    timeout=45.0  # 45 second timeout
                )

                # Phase 4: Result validation
                if not self._validate_llm_result(llm_result):
                    raise ValueError("Invalid LLM result structure")

                # Phase 5: Create and store analysis
                analysis = AgentAnalysis(
                    id=analysis_id,
                    agent_type=self.agent_type,
                    asset_id=asset.id,
                    score=llm_result["score"],
                    confidence=llm_result["confidence"],
                    reasoning=llm_result["reasoning"],
                    factors={f"factor_{i}": factor for i, factor in enumerate(llm_result["key_factors"])},
                    data_sources=self.data_sources,
                    execution_time=llm_result["execution_time"]
                )

                # Store with retry logic
                await self._store_analysis_with_retry(asset, analysis)

                # Update metrics
                self.analysis_count += 1
                self.total_execution_time += llm_result["execution_time"]

                execution_time = time.time() - start_time
                self.logger.info(
                    f"Analysis completed for {asset.symbol}: score={analysis.score}, "
                    f"confidence={analysis.confidence:.1%}, time={execution_time:.2f}s, "
                    f"attempt={attempt + 1}"
                )

                return analysis

            except asyncio.TimeoutError:
                self.logger.warning(f"Analysis timeout for {asset.symbol} on attempt {attempt + 1}")
                if attempt < max_retries:
                    await asyncio.sleep(min(2 ** attempt, 10))  # Exponential backoff, max 10s
                    continue

            except Exception as e:
                self.logger.error(f"Analysis failed for {asset.symbol} on attempt {attempt + 1}: {str(e)}")
                if attempt < max_retries:
                    await asyncio.sleep(min(2 ** attempt, 10))  # Exponential backoff, max 10s
                    continue

        # All attempts failed - return graceful fallback
        self.error_count += 1
        execution_time = time.time() - start_time

        self.logger.error(f"All analysis attempts failed for {asset.symbol} after {max_retries + 1} attempts")

        return self._create_fallback_analysis(asset, analysis_id, execution_time, "All retry attempts exhausted")

    async def _gather_market_data_with_fallback(self, asset_id: str) -> List[Dict[str, Any]]:
        """Gather market data with fallback mechanisms."""
        try:
            # Try primary data source with timeout
            return await asyncio.wait_for(
                self._gather_market_data(asset_id),
                timeout=10.0
            )
        except asyncio.TimeoutError:
            self.logger.warning(f"Market data gathering timeout for {asset_id}")
            return []
        except Exception as e:
            self.logger.warning(f"Market data gathering failed for {asset_id}: {e}")
            return []

    async def _gather_historical_context_with_fallback(self, asset_id: str) -> str:
        """Gather historical context with fallback mechanisms."""
        try:
            return await asyncio.wait_for(
                self._gather_historical_context(asset_id),
                timeout=10.0
            )
        except asyncio.TimeoutError:
            self.logger.warning(f"Historical context gathering timeout for {asset_id}")
            return "Historical context unavailable due to timeout"
        except Exception as e:
            self.logger.warning(f"Historical context gathering failed for {asset_id}: {e}")
            return "Historical context unavailable due to error"

    async def _generate_analysis_with_timeout(self, system_prompt: str, user_prompt: str, timeout: float) -> Dict[str, Any]:
        """Generate LLM analysis with timeout protection."""
        try:
            return await asyncio.wait_for(
                self.llm_client.generate_analysis(
                    system_prompt=system_prompt,
                    user_prompt=user_prompt
                ),
                timeout=timeout
            )
        except asyncio.TimeoutError:
            raise asyncio.TimeoutError(f"LLM analysis timeout after {timeout}s")

    def _validate_llm_result(self, result: Dict[str, Any]) -> bool:
        """Validate LLM result structure and content."""
        required_keys = ["score", "confidence", "reasoning", "key_factors"]

        # Check required keys exist
        if not all(key in result for key in required_keys):
            return False

        # Validate score range
        if not (0 <= result["score"] <= 100):
            return False

        # Validate confidence range
        if not (0 <= result["confidence"] <= 1):
            return False

        # Validate reasoning is not empty
        if not result["reasoning"] or len(result["reasoning"].strip()) < 10:
            return False

        return True

    async def _store_analysis_with_retry(self, asset: CryptoAsset, analysis: AgentAnalysis, max_retries: int = 2):
        """Store analysis with retry logic."""
        for attempt in range(max_retries + 1):
            try:
                # Ensure asset exists before storing analysis
                await db_manager.ensure_asset_exists(asset)

                # Store analysis
                await db_manager.insert_agent_analysis(analysis)
                return

            except Exception as e:
                if attempt < max_retries:
                    self.logger.warning(f"Analysis storage failed on attempt {attempt + 1}: {e}")
                    await asyncio.sleep(0.5 * (attempt + 1))  # Linear backoff
                    continue
                else:
                    self.logger.error(f"Analysis storage failed after {max_retries + 1} attempts: {e}")
                    raise

    def _create_fallback_analysis(self, asset: CryptoAsset, analysis_id: str, execution_time: float, error_msg: str) -> AgentAnalysis:
        """Create a fallback analysis when all attempts fail."""
        return AgentAnalysis(
            id=analysis_id,
            agent_type=self.agent_type,
            asset_id=asset.id,
            score=50.0,  # Neutral score
            confidence=0.1,  # Low confidence
            reasoning=f"Analysis failed: {error_msg}. Using fallback neutral assessment.",
            factors={"error": error_msg, "fallback": "true"},
            data_sources=self.data_sources,
            execution_time=execution_time
        )

    async def _gather_market_data(self, asset_id: str) -> List[Dict[str, Any]]:
        """Gather relevant market data for analysis."""
        try:
            # Get recent market data from database
            async with db_manager.get_connection() as conn:
                results = conn.execute("""
                    SELECT data_type, value, source, confidence, timestamp
                    FROM market_data
                    WHERE asset_id = ?
                    ORDER BY timestamp DESC
                    LIMIT 20
                """, [asset_id]).fetchall()

                market_data = []
                for row in results:
                    market_data.append({
                        'data_type': row[0],
                        'value': row[1],
                        'source': row[2],
                        'confidence': row[3],
                        'timestamp': row[4]
                    })

                return market_data

        except Exception as e:
            self.logger.error(f"Failed to gather market data: {str(e)}")
            return []
    
    async def _gather_historical_context(self, asset_id: str) -> str:
        """Gather historical context for analysis."""
        try:
            # Get asset information and recent analysis
            async with db_manager.get_connection() as conn:
                # Get asset details
                asset_result = conn.execute("""
                    SELECT symbol, name, market_cap, volume_24h, price_usd, price_change_24h, rank
                    FROM crypto_assets
                    WHERE id = ?
                """, [asset_id]).fetchone()

                if not asset_result:
                    return "Asset information not available"

                symbol, name, market_cap, volume_24h, price_usd, price_change_24h, rank = asset_result

                # Get recent analysis history
                analysis_results = conn.execute("""
                    SELECT agent_type, score, confidence, timestamp
                    FROM agent_analysis
                    WHERE asset_id = ?
                    ORDER BY timestamp DESC
                    LIMIT 5
                """, [asset_id]).fetchall()

                # Format values safely
                price_str = f"${price_usd:.2f}" if price_usd else "N/A"
                mcap_str = f"${market_cap:,.0f}" if market_cap else "N/A"
                vol_str = f"${volume_24h:,.0f}" if volume_24h else "N/A"
                change_str = f"{price_change_24h:.2f}%" if price_change_24h else "N/A"
                rank_str = f"#{rank}" if rank else "N/A"

                context = f"""
Asset: {name} ({symbol})
Current Price: {price_str}
Market Cap: {mcap_str}
24h Volume: {vol_str}
24h Change: {change_str}
Market Rank: {rank_str}

Recent Analysis History:
"""

                if analysis_results:
                    for agent_type, score, confidence, timestamp in analysis_results:
                        # Handle potential None values safely
                        score_str = f"{score:.1f}" if score is not None else "N/A"
                        conf_str = f"{confidence:.2f}" if confidence is not None else "N/A"
                        context += f"- {agent_type}: Score {score_str}, Confidence {conf_str} ({timestamp})\n"
                else:
                    context += "- No previous analysis available\n"

                return context.strip()

        except Exception as e:
            self.logger.error("Failed to gather historical context", error=str(e))
            return f"Historical context unavailable: {str(e)}"
    
    def _format_market_data(self, market_data: List[Dict[str, Any]]) -> str:
        """Format market data for LLM consumption."""
        if not market_data:
            return "No market data available"
        
        formatted = []
        for data in market_data[:10]:  # Limit to 10 most recent
            formatted.append(f"- {data.get('data_type', 'unknown')}: {data.get('value', 'N/A')}")
        
        return "\n".join(formatted)
    
    @abstractmethod
    async def _get_focus_areas(self) -> str:
        """Get focus areas specific to this agent type."""
        pass
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get agent performance metrics."""
        avg_execution_time = (
            self.total_execution_time / self.analysis_count 
            if self.analysis_count > 0 else 0.0
        )
        
        return {
            "agent_type": self.agent_type.value,
            "analysis_count": self.analysis_count,
            "error_count": self.error_count,
            "success_rate": (self.analysis_count - self.error_count) / max(self.analysis_count, 1),
            "avg_execution_time": avg_execution_time,
            "data_sources": [ds.value for ds in self.data_sources]
        }
