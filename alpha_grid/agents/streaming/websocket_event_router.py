"""
WebSocket Event Router - Central orchestration of streaming agentic system
==========================================================================

This module provides the main orchestration layer for the streaming agentic system.
It integrates WebSocket data streams with intelligent event detection, classification,
and agent activation to create a revolutionary real-time analysis system.

Key Features:
- Real-time WebSocket data processing
- Intelligent event detection and classification
- Dynamic agent selection and activation
- Streaming consensus formation
- Performance monitoring and optimization
- Fault tolerance and graceful degradation

This router creates the competitive advantage through sub-second decision making
and sophisticated multi-agent intelligence.
"""

import asyncio
import time
import json
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import structlog
from concurrent.futures import ThreadPoolExecutor

from .market_event_detector import MarketEventDetector, MarketEvent, DetectionConfig
from .evidence_based_event_detector import EvidenceBasedEventDetector
from .event_classifier import EventClassifier, ClassifiedEvent
from .agent_selector import AgentSelector, SelectionResult
from agents.scrapers.coinbase_websocket_scraper import CoinbaseWebSocketScraper
from core.models import AgentType

logger = structlog.get_logger(__name__)


class RouterState(Enum):
    """States of the event router."""
    INITIALIZING = "initializing"
    RUNNING = "running"
    PAUSED = "paused"
    STOPPING = "stopping"
    STOPPED = "stopped"
    ERROR = "error"


@dataclass
class RouterConfig:
    """Configuration for the WebSocket event router."""
    # WebSocket configuration
    websocket_products: List[str] = field(default_factory=lambda: [
        "BTC-USD", "ETH-USD", "SOL-USD", "ADA-USD", "DOT-USD",
        "AVAX-USD", "MATIC-USD", "LINK-USD", "UNI-USD", "LTC-USD"
    ])
    websocket_channels: List[str] = field(default_factory=lambda: ["ticker", "heartbeats"])
    
    # Event detection configuration
    detection_config: Optional[DetectionConfig] = None
    
    # Performance configuration
    max_concurrent_analyses: int = 10
    event_queue_size: int = 1000
    analysis_timeout: float = 30.0
    
    # Monitoring configuration
    metrics_update_interval: float = 10.0
    performance_log_interval: float = 60.0
    
    # Error handling
    max_consecutive_errors: int = 5
    error_cooldown_seconds: float = 30.0


@dataclass
class AnalysisRequest:
    """Request for agent analysis."""
    request_id: str
    classified_event: ClassifiedEvent
    selection_result: SelectionResult
    timestamp: datetime
    priority: int = 0  # Higher number = higher priority
    
    def __lt__(self, other):
        """For priority queue ordering."""
        return self.priority > other.priority


@dataclass
class RouterMetrics:
    """Performance metrics for the router."""
    events_processed: int = 0
    events_detected: int = 0
    analyses_completed: int = 0
    analyses_failed: int = 0
    avg_processing_time: float = 0.0
    avg_analysis_time: float = 0.0
    current_queue_size: int = 0
    active_analyses: int = 0
    uptime_seconds: float = 0.0
    error_count: int = 0
    last_error: Optional[str] = None


class WebSocketEventRouter:
    """
    Revolutionary WebSocket Event Router for streaming agentic system.
    
    This router creates the core competitive advantage by providing:
    1. Real-time market event detection from WebSocket streams
    2. Intelligent event classification and prioritization
    3. Dynamic agent selection and activation
    4. Parallel analysis execution with resource management
    5. Streaming results and consensus formation
    
    The system achieves sub-second decision making through sophisticated
    event-driven architecture and optimized agent orchestration.
    """
    
    def __init__(self, config: Optional[RouterConfig] = None,
                 event_callback: Optional[Callable[[ClassifiedEvent], None]] = None,
                 result_callback: Optional[Callable[[Any], None]] = None):
        """
        Initialize the WebSocket event router.
        
        Args:
            config: Router configuration
            event_callback: Callback for detected events
            result_callback: Callback for analysis results
        """
        self.config = config or RouterConfig()
        self.event_callback = event_callback
        self.result_callback = result_callback
        
        self.logger = logger.bind(component="WebSocketEventRouter")
        
        # State management
        self.state = RouterState.INITIALIZING
        self.start_time = datetime.now()
        
        # Core components
        self.websocket_scraper: Optional[CoinbaseWebSocketScraper] = None
        # Use evidence-based event detector for more accurate thresholds
        self.event_detector = EvidenceBasedEventDetector()
        self.event_classifier = EventClassifier()
        self.agent_selector = AgentSelector()
        
        # Event processing
        self.event_queue = asyncio.Queue(maxsize=self.config.event_queue_size)
        self.analysis_queue = asyncio.PriorityQueue(maxsize=self.config.event_queue_size)
        self.active_analyses: Dict[str, asyncio.Task] = {}
        
        # Performance tracking
        self.metrics = RouterMetrics()
        self.processing_times = []
        self.analysis_times = []
        
        # Error handling
        self.consecutive_errors = 0
        self.last_error_time: Optional[datetime] = None
        
        # Background tasks
        self.background_tasks: List[asyncio.Task] = []
        
        # Thread pool for CPU-intensive tasks
        self.thread_pool = ThreadPoolExecutor(max_workers=4)
        
        self.logger.info("WebSocket event router initialized",
                        products=len(self.config.websocket_products),
                        channels=self.config.websocket_channels,
                        max_concurrent=self.config.max_concurrent_analyses)
    
    async def start(self) -> bool:
        """
        Start the WebSocket event router.
        
        Returns:
            True if started successfully, False otherwise
        """
        try:
            self.logger.info("Starting WebSocket event router...")
            self.state = RouterState.INITIALIZING
            
            # Initialize WebSocket connection
            self.websocket_scraper = CoinbaseWebSocketScraper()
            
            # Test connection
            if not await self.websocket_scraper.health_check():
                self.logger.error("WebSocket health check failed")
                return False
            
            # Connect to WebSocket
            success = await self.websocket_scraper.connect(self.config.websocket_products)
            if not success:
                self.logger.error("Failed to connect to WebSocket")
                return False
            
            # Start background tasks
            self.background_tasks = [
                asyncio.create_task(self._websocket_data_processor()),
                asyncio.create_task(self._event_processor()),
                asyncio.create_task(self._analysis_processor()),
                asyncio.create_task(self._metrics_updater()),
                asyncio.create_task(self._performance_logger())
            ]
            
            self.state = RouterState.RUNNING
            self.start_time = datetime.now()
            
            self.logger.info("WebSocket event router started successfully")
            return True
            
        except Exception as e:
            self.logger.error("Failed to start WebSocket event router", error=str(e))
            self.state = RouterState.ERROR
            return False
    
    async def stop(self):
        """Stop the WebSocket event router."""
        try:
            self.logger.info("Stopping WebSocket event router...")
            self.state = RouterState.STOPPING
            
            # Cancel background tasks
            for task in self.background_tasks:
                task.cancel()
            
            # Wait for tasks to complete
            if self.background_tasks:
                await asyncio.gather(*self.background_tasks, return_exceptions=True)
            
            # Cancel active analyses
            for analysis_task in self.active_analyses.values():
                analysis_task.cancel()
            
            if self.active_analyses:
                await asyncio.gather(*self.active_analyses.values(), return_exceptions=True)
            
            # Disconnect WebSocket
            if self.websocket_scraper:
                await self.websocket_scraper.disconnect()
            
            # Shutdown thread pool
            self.thread_pool.shutdown(wait=True)
            
            self.state = RouterState.STOPPED
            self.logger.info("WebSocket event router stopped")
            
        except Exception as e:
            self.logger.error("Error stopping WebSocket event router", error=str(e))
            self.state = RouterState.ERROR
    
    async def _websocket_data_processor(self):
        """Process incoming WebSocket data and detect events."""
        self.logger.info("Starting WebSocket data processor")
        
        try:
            while self.state == RouterState.RUNNING:
                try:
                    # Get real-time data from WebSocket
                    if self.websocket_scraper and self.websocket_scraper.is_connected:
                        data = await self.websocket_scraper.get_real_time_data()
                        
                        if data:
                            await self._process_websocket_data(data)
                    
                    # Small delay to prevent excessive CPU usage
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    self.logger.error("Error in WebSocket data processor", error=str(e))
                    await self._handle_error(e)
                    
        except asyncio.CancelledError:
            self.logger.info("WebSocket data processor cancelled")
        except Exception as e:
            self.logger.error("WebSocket data processor failed", error=str(e))
            self.state = RouterState.ERROR
    
    async def _process_websocket_data(self, data: Dict[str, Any]):
        """Process WebSocket data and detect market events."""
        start_time = time.time()
        
        try:
            for product_id, ticker_data in data.items():
                if not ticker_data:
                    continue
                
                # Detect events from ticker data
                events = self.event_detector.process_ticker_data(product_id, ticker_data)
                
                # Queue detected events for processing
                for event in events:
                    try:
                        await self.event_queue.put(event)
                        self.metrics.events_detected += 1
                        
                        # Call event callback if provided
                        if self.event_callback:
                            # Run callback in thread pool to avoid blocking
                            self.thread_pool.submit(self.event_callback, event)
                            
                    except asyncio.QueueFull:
                        self.logger.warning("Event queue full, dropping event",
                                          event_id=event.event_id)
            
            # Update metrics
            processing_time = time.time() - start_time
            self.processing_times.append(processing_time)
            self.metrics.events_processed += 1
            
            # Keep only recent processing times
            if len(self.processing_times) > 100:
                self.processing_times = self.processing_times[-100:]
            
        except Exception as e:
            self.logger.error("Error processing WebSocket data", error=str(e))
            raise
    
    async def _event_processor(self):
        """Process detected events through classification and agent selection."""
        self.logger.info("Starting event processor")
        
        try:
            while self.state == RouterState.RUNNING:
                try:
                    # Get event from queue
                    event = await asyncio.wait_for(
                        self.event_queue.get(),
                        timeout=1.0
                    )
                    
                    # Process event
                    await self._process_event(event)
                    
                except asyncio.TimeoutError:
                    # No events to process, continue
                    continue
                except Exception as e:
                    self.logger.error("Error in event processor", error=str(e))
                    await self._handle_error(e)
                    
        except asyncio.CancelledError:
            self.logger.info("Event processor cancelled")
        except Exception as e:
            self.logger.error("Event processor failed", error=str(e))
            self.state = RouterState.ERROR
    
    async def _process_event(self, event: MarketEvent):
        """Process a single market event."""
        try:
            # Classify the event
            classified_event = self.event_classifier.classify_event(event)
            
            # Select agents for analysis
            selection_result = self.agent_selector.select_agents(classified_event)
            
            # Create analysis request
            priority = self._calculate_priority(classified_event)
            analysis_request = AnalysisRequest(
                request_id=f"analysis_{event.event_id}_{int(time.time())}",
                classified_event=classified_event,
                selection_result=selection_result,
                timestamp=datetime.now(),
                priority=priority
            )
            
            # Queue for analysis
            try:
                await self.analysis_queue.put(analysis_request)
            except asyncio.QueueFull:
                self.logger.warning("Analysis queue full, dropping request",
                                  event_id=event.event_id)
            
        except Exception as e:
            self.logger.error("Error processing event",
                            event_id=event.event_id,
                            error=str(e))
            raise
    
    def _calculate_priority(self, classified_event: ClassifiedEvent) -> int:
        """Calculate priority for analysis request."""
        base_priority = {
            "emergency": 100,
            "critical": 80,
            "high": 60,
            "medium": 40,
            "low": 20
        }.get(classified_event.original_event.urgency.value, 30)
        
        # Adjust based on risk score
        risk_adjustment = int(classified_event.risk_score * 20)
        
        # Adjust based on market impact
        impact_adjustment = int(classified_event.market_impact_score * 10)
        
        return base_priority + risk_adjustment + impact_adjustment
    
    async def _analysis_processor(self):
        """Process analysis requests and execute agent analyses."""
        self.logger.info("Starting analysis processor")
        
        try:
            while self.state == RouterState.RUNNING:
                try:
                    # Check if we can start new analysis
                    if len(self.active_analyses) >= self.config.max_concurrent_analyses:
                        await asyncio.sleep(0.1)
                        continue
                    
                    # Get analysis request
                    analysis_request = await asyncio.wait_for(
                        self.analysis_queue.get(),
                        timeout=1.0
                    )
                    
                    # Start analysis
                    await self._start_analysis(analysis_request)
                    
                except asyncio.TimeoutError:
                    # No analysis requests, continue
                    continue
                except Exception as e:
                    self.logger.error("Error in analysis processor", error=str(e))
                    await self._handle_error(e)
                    
        except asyncio.CancelledError:
            self.logger.info("Analysis processor cancelled")
        except Exception as e:
            self.logger.error("Analysis processor failed", error=str(e))
            self.state = RouterState.ERROR
    
    async def _start_analysis(self, request: AnalysisRequest):
        """Start an agent analysis."""
        try:
            # Create analysis task
            task = asyncio.create_task(
                self._execute_analysis(request)
            )
            
            # Track active analysis
            self.active_analyses[request.request_id] = task
            
            # Set up completion callback
            task.add_done_callback(
                lambda t: self._analysis_completed(request.request_id, t)
            )
            
        except Exception as e:
            self.logger.error("Error starting analysis",
                            request_id=request.request_id,
                            error=str(e))
    
    async def _execute_analysis(self, request: AnalysisRequest):
        """Execute the actual agent analysis."""
        start_time = time.time()
        
        try:
            # This is where we would integrate with the actual agentic workflow
            # For now, simulate analysis
            await asyncio.sleep(2.0)  # Simulate analysis time
            
            # Create mock result
            result = {
                "request_id": request.request_id,
                "event_id": request.classified_event.original_event.event_id,
                "agents_used": [agent.value for agent in request.selection_result.selected_agents],
                "execution_time": time.time() - start_time,
                "success": True,
                "confidence": request.selection_result.confidence_score,
                "recommendation": "HOLD",  # Mock recommendation
                "analysis_summary": "Mock analysis completed successfully"
            }
            
            # Call result callback if provided
            if self.result_callback:
                self.thread_pool.submit(self.result_callback, result)
            
            # Update metrics
            analysis_time = time.time() - start_time
            self.analysis_times.append(analysis_time)
            self.metrics.analyses_completed += 1
            
            return result
            
        except Exception as e:
            self.logger.error("Analysis execution failed",
                            request_id=request.request_id,
                            error=str(e))
            self.metrics.analyses_failed += 1
            raise
    
    def _analysis_completed(self, request_id: str, task: asyncio.Task):
        """Handle analysis completion."""
        try:
            # Remove from active analyses
            self.active_analyses.pop(request_id, None)
            
            if task.exception():
                self.logger.error("Analysis task failed",
                                request_id=request_id,
                                error=str(task.exception()))
            else:
                self.logger.debug("Analysis completed successfully",
                                request_id=request_id)
                
        except Exception as e:
            self.logger.error("Error handling analysis completion",
                            request_id=request_id,
                            error=str(e))
    
    async def _metrics_updater(self):
        """Update performance metrics periodically."""
        while self.state == RouterState.RUNNING:
            try:
                await self._update_metrics()
                await asyncio.sleep(self.config.metrics_update_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error("Error updating metrics", error=str(e))
    
    async def _update_metrics(self):
        """Update current metrics."""
        try:
            # Update timing metrics
            if self.processing_times:
                self.metrics.avg_processing_time = sum(self.processing_times) / len(self.processing_times)
            
            if self.analysis_times:
                self.metrics.avg_analysis_time = sum(self.analysis_times) / len(self.analysis_times)
            
            # Update queue sizes
            self.metrics.current_queue_size = self.event_queue.qsize() + self.analysis_queue.qsize()
            self.metrics.active_analyses = len(self.active_analyses)
            
            # Update uptime
            self.metrics.uptime_seconds = (datetime.now() - self.start_time).total_seconds()
            
        except Exception as e:
            self.logger.error("Error updating metrics", error=str(e))
    
    async def _performance_logger(self):
        """Log performance metrics periodically."""
        while self.state == RouterState.RUNNING:
            try:
                await asyncio.sleep(self.config.performance_log_interval)
                await self._log_performance()
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error("Error logging performance", error=str(e))
    
    async def _log_performance(self):
        """Log current performance metrics."""
        self.logger.info("Performance metrics",
                        events_processed=self.metrics.events_processed,
                        events_detected=self.metrics.events_detected,
                        analyses_completed=self.metrics.analyses_completed,
                        analyses_failed=self.metrics.analyses_failed,
                        avg_processing_time_ms=self.metrics.avg_processing_time * 1000,
                        avg_analysis_time_s=self.metrics.avg_analysis_time,
                        queue_size=self.metrics.current_queue_size,
                        active_analyses=self.metrics.active_analyses,
                        uptime_hours=self.metrics.uptime_seconds / 3600)
    
    async def _handle_error(self, error: Exception):
        """Handle errors with exponential backoff."""
        self.consecutive_errors += 1
        self.metrics.error_count += 1
        self.metrics.last_error = str(error)
        self.last_error_time = datetime.now()
        
        if self.consecutive_errors >= self.config.max_consecutive_errors:
            self.logger.error("Too many consecutive errors, pausing router",
                            error_count=self.consecutive_errors)
            self.state = RouterState.PAUSED
            await asyncio.sleep(self.config.error_cooldown_seconds)
            self.state = RouterState.RUNNING
            self.consecutive_errors = 0
        else:
            # Exponential backoff
            backoff_time = min(2 ** self.consecutive_errors, 30)
            await asyncio.sleep(backoff_time)
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current router metrics."""
        return {
            "router_state": self.state.value,
            "router_metrics": {
                "events_processed": self.metrics.events_processed,
                "events_detected": self.metrics.events_detected,
                "analyses_completed": self.metrics.analyses_completed,
                "analyses_failed": self.metrics.analyses_failed,
                "avg_processing_time_ms": self.metrics.avg_processing_time * 1000,
                "avg_analysis_time_s": self.metrics.avg_analysis_time,
                "current_queue_size": self.metrics.current_queue_size,
                "active_analyses": self.metrics.active_analyses,
                "uptime_seconds": self.metrics.uptime_seconds,
                "error_count": self.metrics.error_count,
                "last_error": self.metrics.last_error
            },
            "component_metrics": {
                "event_detector": self.event_detector.get_performance_metrics(),
                "event_classifier": self.event_classifier.get_performance_metrics(),
                "agent_selector": self.agent_selector.get_performance_metrics()
            }
        }
