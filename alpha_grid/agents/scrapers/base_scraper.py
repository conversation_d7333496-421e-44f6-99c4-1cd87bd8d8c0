"""
Base scraper class with rate limiting, circuit breaker, and retry logic.
"""

import asyncio
import time
import logging
from typing import Dict, Any, Optional, List
from abc import ABC, abstractmethod
import httpx
from tenacity import retry, stop_after_attempt, wait_exponential, before_sleep_log, retry_if_not_exception_type
from pybreaker import Circuit<PERSON>reaker
import structlog

from ...core.config import config_manager
from ...core.circuit_breaker import get_circuit_breaker, CircuitBreakerConfig, CircuitBreakerOpenError


logger = structlog.get_logger(__name__)


class TokenBucket:
    """Token bucket rate limiter implementation."""
    
    def __init__(self, rate: int, capacity: Optional[int] = None):
        """
        Initialize token bucket.
        
        Args:
            rate: Tokens per minute
            capacity: Maximum tokens (defaults to rate)
        """
        self.rate = rate / 60.0  # Convert to tokens per second
        self.capacity = capacity or rate
        self.tokens = self.capacity
        self.last_update = time.time()
        self._lock = asyncio.Lock()
    
    async def acquire(self, tokens: int = 1) -> bool:
        """Acquire tokens from bucket."""
        async with self._lock:
            now = time.time()
            # Add tokens based on elapsed time
            elapsed = now - self.last_update
            self.tokens = min(self.capacity, self.tokens + elapsed * self.rate)
            self.last_update = now
            
            if self.tokens >= tokens:
                self.tokens -= tokens
                return True
            return False
    
    async def wait_for_tokens(self, tokens: int = 1):
        """Wait until tokens are available."""
        while not await self.acquire(tokens):
            await asyncio.sleep(0.1)


class BaseScraper(ABC):
    """Base class for all API scrapers with reliability features."""
    
    def __init__(
        self,
        name: str,
        rate_limit: int,
        api_key: Optional[str] = None,
        base_url: Optional[str] = None
    ):
        """
        Initialize base scraper.
        
        Args:
            name: Scraper name for logging
            rate_limit: Requests per minute
            api_key: API key if required
            base_url: Base URL for API
        """
        self.name = name
        self.rate_limit = rate_limit
        self.api_key = api_key
        self.base_url = base_url
        
        # Rate limiting
        self.token_bucket = TokenBucket(rate_limit)
        
        # HTTP client
        self.client = httpx.AsyncClient(
            timeout=config_manager.settings.request_timeout,
            headers=self._get_default_headers()
        )
        
        # Advanced circuit breaker
        cb_config = CircuitBreakerConfig(
            failure_threshold=config_manager.settings.failure_threshold,
            recovery_timeout=config_manager.settings.recovery_timeout,
            timeout=config_manager.settings.request_timeout,
            expected_exceptions=(httpx.HTTPError, httpx.TimeoutException, ConnectionError)
        )
        self.circuit_breaker = get_circuit_breaker(f"{name}_api", cb_config)
        
        # Metrics
        self.request_count = 0
        self.error_count = 0
        self.last_request_time = 0
        
        # Logger
        self.logger = logger.bind(scraper=name)
    
    def _get_default_headers(self) -> Dict[str, str]:
        """Get default HTTP headers."""
        return {
            "User-Agent": "Alpha-Grid/1.0.0",
            "Accept": "application/json",
            "Content-Type": "application/json"
        }
    
    @retry(
        stop=stop_after_attempt(3),  # Reduced from 5 to 3
        wait=wait_exponential(multiplier=1, max=30),  # Reduced max wait
        before_sleep=before_sleep_log(logger, logging.WARNING)
    )
    async def _make_request(
        self,
        url: str,
        method: str = "GET",
        params: Optional[Dict] = None,
        headers: Optional[Dict] = None,
        **kwargs
    ) -> Dict[Any, Any]:
        """Make HTTP request with retry logic."""
        
        # Wait for rate limit
        await self.token_bucket.wait_for_tokens()
        
        # Prepare request
        request_headers = self._get_default_headers()
        if headers:
            request_headers.update(headers)
        
        # Add API key if required
        if self.api_key:
            request_headers.update(self._get_auth_headers())
        
        self.logger.debug(
            "Making request",
            url=url,
            method=method,
            params=params
        )
        
        try:
            # Make request through circuit breaker
            response = await self.circuit_breaker.call(
                self.client.request,
                method,
                url,
                params=params,
                headers=request_headers,
                **kwargs
            )
            
            response.raise_for_status()
            
            # Update metrics
            self.request_count += 1
            self.last_request_time = time.time()
            
            # Parse JSON response
            data = response.json()
            
            self.logger.debug(
                "Request successful",
                status_code=response.status_code,
                response_size=len(str(data))
            )
            
            return data
            
        except CircuitBreakerOpenError as e:
            self.error_count += 1
            self.logger.warning(
                "Request blocked by circuit breaker",
                url=url,
                circuit_state=self.circuit_breaker.state.value
            )
            raise

        except httpx.TimeoutException as e:
            self.error_count += 1
            self.logger.warning(
                "Request timed out",
                url=url,
                timeout=config_manager.settings.request_timeout
            )
            raise

        except httpx.HTTPStatusError as e:
            self.error_count += 1
            self.logger.warning(
                "HTTP error response",
                url=url,
                status_code=e.response.status_code,
                response_text=e.response.text[:200]
            )
            raise

        except httpx.RequestError as e:
            self.error_count += 1
            self.logger.error(
                "Request error",
                url=url,
                error=str(e),
                error_type=type(e).__name__
            )
            raise

        except Exception as e:
            self.error_count += 1
            self.logger.error(
                "Unexpected error",
                error=str(e),
                url=url,
                error_type=type(e).__name__,
                error_count=self.error_count
            )
            raise
    
    @abstractmethod
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers. Override in subclasses."""
        return {}
    
    @abstractmethod
    async def health_check(self) -> bool:
        """Check if the API is healthy. Override in subclasses."""
        pass
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get scraper metrics."""
        return {
            "name": self.name,
            "request_count": self.request_count,
            "error_count": self.error_count,
            "error_rate": self.error_count / max(self.request_count, 1),
            "last_request_time": self.last_request_time,
            "circuit_breaker_state": self.circuit_breaker.state.value,
            "rate_limit": self.rate_limit,
            "tokens_available": self.token_bucket.tokens
        }
    
    async def close(self):
        """Close HTTP client."""
        await self.client.aclose()
    
    def get_health(self) -> Dict[str, Any]:
        """Returns the health status of the scraper."""
        # A more robust implementation would track requests and errors.
        return {
            "status": "ok",
            "last_request_successful": True, # Placeholder
        }
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
