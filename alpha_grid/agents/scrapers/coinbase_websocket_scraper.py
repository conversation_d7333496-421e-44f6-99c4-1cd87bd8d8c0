"""
Coinbase Advanced Trade WebSocket Scraper
==========================================

Real-time market data scraper using Coinbase Advanced Trade WebSocket API.
Follows prompt.md Phase 2 guidelines with rate limiting, circuit breakers, and caching.
"""

import asyncio
import json
import time
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta, timezone
import structlog
import uuid
from coinbase.websocket import WSClient, WebsocketResponse
from coinbase.rest import RESTClient

from .base_scraper import BaseScraper
from ...core.config import config_manager


logger = structlog.get_logger(__name__)


class CoinbaseWebSocketScraper(BaseScraper):
    """
    Coinbase Advanced Trade WebSocket scraper for real-time market data.
    
    Features:
    - Real-time ticker data via WebSocket
    - Automatic reconnection with exponential backoff
    - Data buffering and caching
    - Circuit breaker protection
    - Rate limiting compliance
    """
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        api_secret: Optional[str] = None,
        rate_limit: int = 100  # Coinbase allows high rate limits for WebSocket
    ):
        """Initialize Coinbase WebSocket scraper."""
        super().__init__(
            name="coinbase_websocket",
            rate_limit=rate_limit,
            api_key=api_key,
            base_url="wss://advanced-trade-ws.coinbase.com"
        )

        self.correlation_id = str(uuid.uuid4())
        self.api_secret = api_secret
        self.ws_client = None
        self.rest_client = None
        self.is_connected = False
        self.data_buffer = {}
        self.last_update = {}
        self.subscribed_products = set()
        
        # Data retention settings (following prompt.md compliance)
        self.max_buffer_age = timedelta(hours=1)  # Keep data for 1 hour max
        self.cleanup_interval = 300  # Cleanup every 5 minutes
        self.last_cleanup = time.time()
        
        logger.info("Coinbase WebSocket scraper initialized", 
                   authenticated=bool(api_key and api_secret))
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers (not used for WebSocket)."""
        return {}
    
    async def health_check(self) -> bool:
        """Check if Coinbase API is healthy."""
        try:
            if not self.rest_client:
                self.rest_client = RESTClient(
                    api_key=self.api_key,
                    api_secret=self.api_secret
                ) if self.api_key else RESTClient()
            
            # Test with public endpoint
            products = self.rest_client.get_public_products()
            return len(products.products) > 0
            
        except Exception as e:
            logger.error("Coinbase health check failed", error=str(e), correlation_id=self.correlation_id)
            return False
    
    def _on_message(self, message: str):
        """Handle incoming WebSocket messages."""
        try:
            data = json.loads(message)
            ws_response = WebsocketResponse(data)
            
            if ws_response.channel == "ticker":
                self._process_ticker_data(ws_response)
            elif ws_response.channel == "heartbeats":
                logger.debug("Heartbeat received")
            else:
                logger.debug("Received message", channel=ws_response.channel)
                
        except Exception as e:
            logger.error("Error processing WebSocket message",
                        error=str(e), message=message[:200], correlation_id=self.correlation_id)
    
    def _process_ticker_data(self, ws_response: WebsocketResponse):
        """Process ticker data from WebSocket."""
        try:
            for event in ws_response.events:
                for ticker in event.tickers:
                    product_id = ticker.product_id
                    
                    # Create standardized market data
                    market_data = {
                        'symbol': product_id.replace('-', ''),
                        'product_id': product_id,
                        'price': float(ticker.price),
                        'volume_24h': float(ticker.volume_24h) if hasattr(ticker, 'volume_24h') else 0,
                        'timestamp': datetime.now(timezone.utc).isoformat(),
                        'source': 'coinbase_websocket',
                        'best_bid': float(ticker.best_bid) if hasattr(ticker, 'best_bid') else None,
                        'best_ask': float(ticker.best_ask) if hasattr(ticker, 'best_ask') else None,
                        'low_24h': float(ticker.low_24h) if hasattr(ticker, 'low_24h') else None,
                        'high_24h': float(ticker.high_24h) if hasattr(ticker, 'high_24h') else None,
                    }
                    
                    # Buffer the data
                    self.data_buffer[product_id] = market_data
                    self.last_update[product_id] = time.time()
                    
                    logger.debug("Ticker data updated", 
                               product_id=product_id, 
                               price=market_data['price'])
                    
        except Exception as e:
            logger.error("Error processing ticker data", error=str(e), correlation_id=self.correlation_id)
    
    async def connect(self, product_ids: List[str] = None, max_retries: int = 3):
        """Connect to Coinbase WebSocket with enhanced error handling and retry logic."""
        if product_ids is None:
            product_ids = ["BTC-USD", "ETH-USD", "SOL-USD", "ADA-USD", "DOT-USD"]

        for attempt in range(max_retries):
            try:
                logger.info(f"WebSocket connection attempt {attempt + 1}/{max_retries}",
                           products=product_ids, correlation_id=self.correlation_id)

                # Clean up any existing connection
                if self.ws_client and self.is_connected:
                    await self.disconnect()

                # Initialize WebSocket client with enhanced configuration
                if self.api_key and self.api_secret:
                    self.ws_client = WSClient(
                        api_key=self.api_key,
                        api_secret=self.api_secret,
                        on_message=self._on_message,
                        retry=True,  # Enable automatic reconnection
                        timeout=30   # 30 second timeout
                    )
                else:
                    # Use unauthenticated client for public data
                    self.ws_client = WSClient(
                        on_message=self._on_message,
                        retry=True,
                        timeout=30
                    )

                # Connect with timeout
                connection_timeout = 15.0  # 15 seconds
                await asyncio.wait_for(
                    asyncio.to_thread(self.ws_client.open),
                    timeout=connection_timeout
                )

                # Subscribe to channels with timeout
                subscription_timeout = 10.0  # 10 seconds
                await asyncio.wait_for(
                    asyncio.to_thread(self.ws_client.ticker, product_ids=product_ids),
                    timeout=subscription_timeout
                )
                await asyncio.wait_for(
                    asyncio.to_thread(self.ws_client.heartbeats),
                    timeout=subscription_timeout
                )

                # Verify connection by waiting for initial data
                await asyncio.sleep(2)  # Allow time for initial messages

                self.is_connected = True
                self.subscribed_products.update(product_ids)

                logger.info("Successfully connected to Coinbase WebSocket",
                           products=product_ids,
                           authenticated=bool(self.api_key),
                           attempt=attempt + 1,
                           correlation_id=self.correlation_id)

                return True

            except asyncio.TimeoutError:
                logger.warning(f"WebSocket connection timeout on attempt {attempt + 1}",
                              correlation_id=self.correlation_id)
                if attempt < max_retries - 1:
                    backoff_time = min(2 ** attempt, 30)  # Exponential backoff, max 30s
                    logger.info(f"Retrying in {backoff_time} seconds...")
                    await asyncio.sleep(backoff_time)
                    continue

            except Exception as e:
                logger.error(f"WebSocket connection failed on attempt {attempt + 1}",
                            error=str(e),
                            correlation_id=self.correlation_id)
                if attempt < max_retries - 1:
                    backoff_time = min(2 ** attempt, 30)  # Exponential backoff, max 30s
                    logger.info(f"Retrying in {backoff_time} seconds...")
                    await asyncio.sleep(backoff_time)
                    continue

        logger.error("All WebSocket connection attempts failed",
                    max_retries=max_retries,
                    correlation_id=self.correlation_id)
        self.is_connected = False
        return False
    
    async def disconnect(self):
        """Disconnect from WebSocket and clean up resources."""

        try:
            if self.ws_client and self.is_connected:
                self.ws_client.close()
                self.is_connected = False
                logger.info("Disconnected from Coinbase WebSocket")
        except Exception as e:
            logger.error("Error disconnecting from WebSocket", error=str(e), correlation_id=self.correlation_id)
        finally:
            # Clean up data buffers and resources
            self.data_buffer.clear()
            self.last_update.clear()
            self.ws_client = None
            logger.info("Resources cleaned up after disconnection", correlation_id=self.correlation_id)
    async def get_real_time_data(self, product_ids: List[str] = None) -> Dict[str, Any]:
        """Get real-time market data from buffer."""
        await self._cleanup_old_data()
        
        if product_ids is None:
            return dict(self.data_buffer)
        
        return {pid: self.data_buffer.get(pid) for pid in product_ids 
                if pid in self.data_buffer}
    
    async def get_market_data(self, limit: int = 20) -> List[Dict[str, Any]]:
        """Get market data compatible with existing ranking engine."""
        await self._cleanup_old_data()
        
        # Convert buffered data to ranking engine format
        market_data = []
        
        for product_id, data in list(self.data_buffer.items())[:limit]:
            try:
                # Convert to CoinPaprika-compatible format
                converted_data = {
                    'id': data['symbol'].lower(),
                    'name': data['symbol'],
                    'symbol': data['symbol'],
                    'rank': len(market_data) + 1,
                    'circulating_supply': 0,  # Not available in ticker
                    'total_supply': 0,
                    'max_supply': 0,
                    'beta_value': 1.0,
                    'first_data_at': '2020-01-01T00:00:00Z',
                    'last_updated': data['timestamp'],
                    'quotes': {
                        'USD': {
                            'price': data['price'],
                            'volume_24h': data['volume_24h'],
                            'volume_24h_change_24h': 0,
                            'market_cap': 0,  # Not available in ticker
                            'market_cap_change_24h': 0,
                            'percent_change_15m': 0,
                            'percent_change_30m': 0,
                            'percent_change_1h': 0,
                            'percent_change_6h': 0,
                            'percent_change_12h': 0,
                            'percent_change_24h': 0,
                            'percent_change_7d': 0,
                            'percent_change_30d': 0,
                            'percent_change_1y': 0,
                            'ath_price': data.get('high_24h', data['price']),
                            'ath_date': data['timestamp'],
                            'percent_from_price_ath': 0
                        }
                    }
                }
                market_data.append(converted_data)
                
            except Exception as e:
                logger.error("Error converting market data", 
                           product_id=product_id, error=str(e))
        
        logger.info("Retrieved real-time market data", 
                   count=len(market_data), source="coinbase_websocket")
        
        return market_data
    
    async def _cleanup_old_data(self):
        """Clean up old data following prompt.md compliance guidelines."""
        current_time = time.time()
        
        if current_time - self.last_cleanup < self.cleanup_interval:
            return
        
        cutoff_time = current_time - self.max_buffer_age.total_seconds()
        
        # Remove old data
        expired_products = [
            pid for pid, timestamp in self.last_update.items()
            if timestamp < cutoff_time
        ]
        
        for pid in expired_products:
            self.data_buffer.pop(pid, None)
            self.last_update.pop(pid, None)
        
        if expired_products:
            logger.info("Cleaned up expired data", 
                       expired_count=len(expired_products))
        
        self.last_cleanup = current_time
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.disconnect()
