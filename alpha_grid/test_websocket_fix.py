#!/usr/bin/env python3
"""
Test WebSocket scraper fix - Minimal test to verify syntax and basic functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_websocket_syntax():
    """Test that the WebSocket scraper has no syntax errors."""
    try:
        print("Testing WebSocket scraper syntax...")

        # Read the file and compile it to check for syntax errors
        with open('agents/scrapers/coinbase_websocket_scraper.py', 'r') as f:
            code = f.read()

        # Try to compile the code
        compile(code, 'coinbase_websocket_scraper.py', 'exec')
        print("✅ Syntax check passed - no syntax errors!")

        # Check for the specific line that was problematic
        lines = code.split('\n')
        line_52 = lines[51] if len(lines) > 51 else ""
        if line_52.strip().startswith('self.correlation_id'):
            if line_52.startswith('        self.correlation_id'):
                print("✅ Line 52 indentation fixed!")
            else:
                print(f"⚠️ Line 52 indentation issue: '{line_52}'")

        return True

    except SyntaxError as e:
        print(f"❌ Syntax error still exists: {e}")
        print(f"   Line {e.lineno}: {e.text}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_coinbase_api_availability():
    """Test if Coinbase API is accessible."""
    try:
        import requests
        response = requests.get("https://api.exchange.coinbase.com/products", timeout=5)
        if response.status_code == 200:
            print("✅ Coinbase API is accessible!")
            return True
        else:
            print(f"⚠️ Coinbase API returned status {response.status_code}")
            return False
    except Exception as e:
        print(f"⚠️ Cannot reach Coinbase API: {e}")
        return False

if __name__ == "__main__":
    print("🔧 WebSocket Fix Validation Test")
    print("=" * 50)
    
    # Test 1: Syntax check
    syntax_success = test_websocket_syntax()
    
    # Test 2: API availability
    api_success = test_coinbase_api_availability()
    
    print("\n📊 Test Results:")
    print(f"  WebSocket Syntax: {'✅ PASS' if syntax_success else '❌ FAIL'}")
    print(f"  Coinbase API:     {'✅ PASS' if api_success else '⚠️ WARN'}")

    if syntax_success:
        print("\n🎯 WebSocket syntax error has been FIXED!")
        print("   Ready to proceed with threshold implementation.")
    else:
        print("\n❌ WebSocket syntax error still exists!")
        print("   Must fix before proceeding.")
